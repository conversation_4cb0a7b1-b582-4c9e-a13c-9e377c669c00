# 🎬 场景管理模块

## 功能描述
提供场景的保存、加载和管理功能，支持完整的地图状态保存。

## 模块信息
- **功能名称**: 场景管理
- **模块类型**: 高级功能
- **优先级**: 中等
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)

## 触发事件
- `scene:activated` - 场景管理激活
- `scene:deactivated` - 场景管理停用
- `scene:saved` - 保存场景
- `scene:loaded` - 加载场景

## 文件结构
```
场景管理/
├── README.md           ← 本文档
├── config.js          ← 模块配置
├── core/              ← 核心逻辑
│   └── SceneManagerTool.js
├── ui/                ← 界面逻辑
│   └── SceneManagerUI.js
├── styles/            ← 样式文件
│   └── sceneManager.css
└── assets/            ← 资源文件
    └── scene-manager.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('场景管理');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-右侧",
  "快捷键": "C",
  "最大场景数": 20,
  "包含实体": true
}
```

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本