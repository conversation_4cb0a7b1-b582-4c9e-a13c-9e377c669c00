/**
 * ⛏️ 地形开挖模块配置文件
 * 
 * 此文件定义了地形开挖功能的所有配置选项
 * 修改此文件可以调整功能行为，无需修改核心代码
 */

const TerrainDigConfig = {
    // 模块基本信息
    module: {
        name: '地形开挖',
        id: 'terrain-dig',
        version: '2.0.0',
        description: '地形开挖和体积计算工具',
        author: 'System',
        category: '高级工具'
    },

    // 功能开关
    features: {
        enabled: true,                    // 是否启用此模块
        useWorker: true,                 // 是否使用Web Worker计算
        realTimeCalculation: true,       // 实时计算体积
        show3DEffect: true,              // 显示3D挖掘效果
        showVolumeResult: true           // 显示体积计算结果
    },

    // 界面配置
    ui: {
        position: '工具栏-左侧',           // 按钮位置
        priority: 2,                     // 显示优先级
        icon: 'terrain-dig.svg',         // 图标文件名
        tooltip: '地形开挖工具',          // 鼠标提示
        shortcut: 'D',                   // 快捷键
        panelWidth: 300,                 // 面板宽度
        panelHeight: 400                 // 面板高度
    },

    // 功能参数
    parameters: {
        defaultDepth: 10,                // 默认挖掘深度(米)
        minDepth: 1,                     // 最小深度(米)
        maxDepth: 100,                   // 最大深度(米)
        depthStep: 0.5,                  // 深度调整步长(米)
        
        // 体积计算精度
        volumeCalculation: {
            samplingDensity: 'medium',    // 采样密度: low, medium, high
            useHeightSamples: true,       // 是否使用高度采样
            precision: 2                  // 结果保留小数位数
        },

        // 3D效果配置
        visualization: {
            fillColor: 'rgba(255, 0, 0, 0.3)',      // 填充颜色
            outlineColor: 'rgba(255, 0, 0, 0.8)',   // 边框颜色
            outlineWidth: 2,                         // 边框宽度
            showLabels: true,                        // 显示标签
            animationDuration: 1000                  // 动画持续时间(毫秒)
        }
    },

    // 依赖模块
    dependencies: [
        'EventBus',           // 事件总线 (必需)
        'WorkerManager',      // Worker管理器 (可选，用于后台计算)
        'PanelManager',       // 面板管理器 (必需)
        'ButtonConfig'        // 按钮配置 (必需)
    ],

    // 事件配置
    events: {
        // 触发的事件
        emit: [
            'terrain-dig:activated',           // 工具激活
            'terrain-dig:deactivated',         // 工具停用
            'terrain-dig:drawing-started',     // 开始绘制
            'terrain-dig:drawing-finished',    // 绘制完成
            'terrain-dig:volume-calculated',   // 体积计算完成
            'terrain-dig:cleared'              // 清除挖掘
        ],
        
        // 监听的事件
        listen: [
            'system:ready',                    // 系统就绪
            'system:cleanup',                  // 系统清理
            'worker:ready',                    // Worker就绪
            'toolbar:tool-changed'             // 工具切换
        ]
    },

    // 错误处理配置
    errorHandling: {
        showUserErrors: true,             // 向用户显示错误
        logLevel: 'warn',                 // 日志级别: error, warn, info, debug
        fallbackToSync: true,             // Worker失败时回退到同步计算
        retryAttempts: 3                  // 重试次数
    },

    // 性能配置
    performance: {
        maxPolygonPoints: 100,            // 最大多边形点数
        debounceDelay: 300,               // 防抖延迟(毫秒)
        enableCaching: true,              // 启用结果缓存
        cacheSize: 50                     // 缓存大小
    }
};

// 导出配置
if (typeof window !== 'undefined') {
    window.TerrainDigConfig = TerrainDigConfig;
}

// ES6 模块导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TerrainDigConfig;
}

console.log('⛏️ 地形开挖模块配置已加载');