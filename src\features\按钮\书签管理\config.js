/**
 * 书签管理模块配置
 */
export const BookmarkConfig = {
    // 模块基本信息
    name: '书签管理',
    id: 'bookmark',
    tooltip: '保存和管理场景书签',
    
    // 图标配置
    icon: {
        path: './assets/bookmark.svg',
        alt: '书签管理'
    },
    
    // 存储配置
    storage: {
        key: 'cesiumBookmarks',
        maxCount: 20,
        defaultName: '未命名书签'
    },
    
    // UI配置
    ui: {
        panel: {
            width: 300,
            height: 400,
            title: '书签管理'
        },
        button: {
            id: 'toggleBookmark',
            class: 'tool-button'
        }
    },
    
    // 功能配置
    features: {
        autoSave: false,
        exportImport: true,
        preview: true
    }
};