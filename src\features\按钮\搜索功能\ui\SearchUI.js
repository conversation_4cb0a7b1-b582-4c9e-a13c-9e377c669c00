/**
 * 搜索功能UI组件
 * 提供地点搜索面板的HTML定义和事件处理
 */
class SearchUI {
    /**
     * 创建搜索UI组件
     * @param {Object} viewer - Cesium viewer实例
     */
    constructor(viewer) {
        this.viewer = viewer;
        this.locationSearch = null;
        this.panelId = 'searchPanel';
        this.toggleBtnId = 'toggleSearch';
    }

    /**
     * 生成搜索组件的HTML代码
     * @returns {string} HTML代码字符串
     */
    generateHTML() {
        return `
        <!-- 搜索按钮 -->
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/搜索功能/assets/search.svg" alt="地点搜索">
            <div class="tooltip">地点搜索</div>
        </button>
        
        <!-- 搜索面板 -->
        <div id="${this.panelId}" style="display: none;">
            <div class="toolbar-panel-title">地点搜索</div>
            <div class="input-group">
                <input type="text" id="searchInput" placeholder="请输入地点名称或经纬度">
                <div class="button-group">
                    <button id="searchButton">搜索</button>
                    <button id="stopSearchButton" class="stop-btn" style="display: none;">停止</button>
                </div>
            </div>
            <div id="searchStatus" style="margin-top: 10px; display: none; color: #666; font-size: 12px;"></div>
        </div>`;
    }

    /**
     * 将按钮添加到工具按钮组
     * @param {string} containerId - 工具按钮组容器ID
     */
    appendButtonTo(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`未找到容器元素: ${containerId}`);
            return;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/搜索功能/assets/search.svg" alt="地点搜索">
            <div class="tooltip">地点搜索</div>
        </button>`;
        
        container.appendChild(tempDiv.firstElementChild);
        console.log(`搜索按钮已添加到: ${containerId}`);
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <!-- 搜索面板 -->
        <div id="${this.panelId}" style="display: none;">
            <div class="toolbar-panel-title">地点搜索</div>
            <div class="input-group">
                <input type="text" id="searchInput" placeholder="请输入地点名称或经纬度">
                <div class="button-group">
                    <button id="searchButton">搜索</button>
                    <button id="stopSearchButton" class="stop-btn" style="display: none;">停止</button>
                </div>
            </div>
            <div id="searchStatus" style="margin-top: 10px; display: none; color: #666; font-size: 12px;"></div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        console.log('搜索面板已添加到页面');
    }

    /**
     * 初始化搜索组件及事件绑定
     */
    init() {
        console.log('正在初始化搜索UI组件...');
        
        // 获取或创建搜索实例
        this.locationSearch = window.locationSearch || LocationSearch.init(this.viewer);
        window.locationSearch = this.locationSearch;
        
        // 切换面板显示/隐藏的全局函数
        window.toggleSearchPanel = () => {
            console.log('直接调用toggleSearchPanel函数');
            const searchPanel = document.getElementById(this.panelId);
            if (searchPanel) {
                const currentDisplay = window.getComputedStyle(searchPanel).display;
                console.log('搜索面板当前显示状态:', currentDisplay);
                
                if (currentDisplay === 'none') {
                    console.log('直接显示搜索面板');
                    searchPanel.style.display = 'block';
                    
                    // 隐藏其他面板
                    ['terrainDigPanel', 'measureToolContainer', 'profileAnalysisPanel'].forEach(id => {
                        const panel = document.getElementById(id);
                        if (panel) panel.style.display = 'none';
                    });
                    
                    // 设置面板位置
                    this.positionPanel();
                } else {
                    console.log('直接隐藏搜索面板');
                    searchPanel.style.display = 'none';
                }
            } else {
                console.error('搜索面板元素不存在');
            }
        };
        
        // 绑定搜索按钮事件
        this.bindEvents();
        
        console.log('搜索UI组件初始化完成');
    }

    /**
     * 设置面板位置
     */
    positionPanel() {
        const panel = document.getElementById(this.panelId);
        const toggleBtn = document.getElementById(this.toggleBtnId);
        
        if (panel && toggleBtn && window.PanelPositioner) {
            try {
                window.PanelPositioner.setPosition(toggleBtn, panel, {
                    preferredPosition: 'right', // 默认显示在右侧
                    gap: 10
                });
            } catch (e) {
                console.error('面板定位出错:', e);
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
            }
        } else {
            console.error('无法设置面板位置:', {
                'panel存在': !!panel,
                'toggleBtn存在': !!toggleBtn,
                'PanelPositioner存在': !!window.PanelPositioner
            });
        }
    }

    /**
     * 绑定事件处理函数
     */
    bindEvents() {
        // 绑定切换按钮事件
        const toggleBtn = document.getElementById(this.toggleBtnId);
        if (toggleBtn) {
            console.log('绑定搜索切换按钮事件');
            
            // 清除可能存在的事件绑定
            const newToggleBtn = toggleBtn.cloneNode(true);
            if (toggleBtn.parentNode) {
                toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
                console.log('已清除并替换搜索按钮以避免事件重复绑定');
            }
            
            newToggleBtn.addEventListener('click', () => {
                console.log('搜索切换按钮被点击');
                window.toggleSearchPanel();
            });
        }
        
        // 绑定搜索按钮事件
        const searchButton = document.getElementById('searchButton');
        if (searchButton) {
            const newSearchButton = searchButton.cloneNode(true);
            if (searchButton.parentNode) {
                searchButton.parentNode.replaceChild(newSearchButton, searchButton);
            }
            
            newSearchButton.addEventListener('click', () => {
                this.performSearch();
            });
        }
        
        // 绑定清除按钮事件
        const stopSearchButton = document.getElementById('stopSearchButton');
        if (stopSearchButton) {
            const newStopSearchButton = stopSearchButton.cloneNode(true);
            if (stopSearchButton.parentNode) {
                stopSearchButton.parentNode.replaceChild(newStopSearchButton, stopSearchButton);
            }
            
            newStopSearchButton.addEventListener('click', () => {
                this.clearSearch();
            });
        }
        
        // 绑定输入框回车事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            const newSearchInput = searchInput.cloneNode(true);
            if (searchInput.parentNode) {
                searchInput.parentNode.replaceChild(newSearchInput, searchInput);
            }
            
            newSearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }
    }

    /**
     * 执行搜索操作
     */
    performSearch() {
        console.log('搜索按钮被点击');
        const searchInput = document.getElementById('searchInput');
        
        if (searchInput && this.locationSearch) {
            const searchText = searchInput.value.trim();
            console.log('搜索文本:', searchText);
            this.locationSearch.searchLocation(searchText);
        } else {
            console.error('搜索失败:', {
                '搜索输入框存在': !!searchInput,
                '搜索对象存在': !!this.locationSearch
            });
        }
    }

    /**
     * 清除搜索结果
     */
    clearSearch() {
        console.log('清除按钮被点击');
        const searchInput = document.getElementById('searchInput');
        
        if (searchInput) {
            searchInput.value = '';
            console.log('输入框已清空');
        }
        
        if (this.locationSearch) {
            this.locationSearch.clearSearchMarker();
        }
    }

    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {SearchUI} 搜索UI实例
     */
    static init(viewer, toolButtonsId = 'toolButtons') {
        console.log('SearchUI.init 被调用');
        const searchUI = new SearchUI(viewer);
        
        // 检查是否需要添加HTML元素
        if (!document.getElementById(searchUI.toggleBtnId)) {
            searchUI.appendButtonTo(toolButtonsId);
        }
        
        if (!document.getElementById(searchUI.panelId)) {
            searchUI.appendPanelToBody();
        }
        
        // 初始化搜索组件
        searchUI.init();
        
        return searchUI;
    }
}

// 导出到全局作用域
window.SearchUI = SearchUI; 
window.SearchUI = SearchUI; 