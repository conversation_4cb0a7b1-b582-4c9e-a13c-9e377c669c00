# ⛏️ 地形开挖模块

## 功能描述
提供地形开挖功能，支持多边形区域挖掘、体积计算和3D可视化效果。

## 模块信息
- **功能名称**: 地形开挖
- **模块类型**: 高级工具
- **优先级**: 中等
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- WorkerManager (后台计算)
- PanelManager (面板管理)

## 触发事件
- `terrain-dig:activated` - 开挖工具激活
- `terrain-dig:deactivated` - 开挖工具停用
- `terrain-dig:volume-calculated` - 体积计算完成

## 监听事件
- `system:ready` - 系统就绪
- `worker:ready` - Worker就绪

## 文件结构
```
地形开挖/
├── README.md           ← 本文档
├── config.js          ← 模块配置(待添加)
├── core/              ← 核心逻辑
│   └── TerrainDigHandler.js
├── ui/                ← 界面逻辑
│   └── TerrainDigUI.js
├── styles/            ← 样式文件
│   └── terrain-dig.css
└── assets/            ← 资源文件
    └── terrain-dig.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('地形开挖');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-左侧",
  "快捷键": "D",
  "默认深度": 10,
  "使用Worker": true
}
```

## 开发说明
- 支持Web Worker后台计算体积
- 使用Cesium的地形采样API
- 实时3D可视化挖掘效果

## 更新日志
- v2.0.0: 标准化模块结构，添加Worker支持
- v1.0.0: 初始版本