# 📊 剖面分析模块

## 功能描述
提供地形剖面分析功能，支持高程剖面图生成和地形分析。

## 模块信息
- **功能名称**: 剖面分析
- **模块类型**: 高级工具
- **优先级**: 高
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- WorkerManager (后台计算)
- ECharts (图表库)

## 触发事件
- `profile:activated` - 剖面分析激活
- `profile:deactivated` - 剖面分析停用
- `profile:analysis-complete` - 分析完成

## 文件结构
```
剖面分析/
├── README.md           ← 本文档
├── config.js          ← 模块配置
├── core/              ← 核心逻辑
│   └── ProfileAnalysis.js
├── ui/                ← 界面逻辑
│   └── ProfileAnalysisUI.js
├── styles/            ← 样式文件
│   └── toolbar-buttons.css
└── assets/            ← 资源文件
    ├── profile-analysis.svg
    ├── clear.svg
    ├── close.svg
    └── draw.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('剖面分析');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-左侧", 
  "快捷键": "P",
  "采样点数": 100,
  "使用Worker": true
}
```

## 更新日志
- v2.0.0: 标准化模块结构，添加Worker支持
- v1.0.0: 初始版本