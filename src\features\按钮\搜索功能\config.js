/**
 * 🔍 搜索功能模块配置文件
 */

const SearchConfig = {
    module: {
        name: '搜索功能',
        id: 'search',
        version: '2.0.0',
        description: '地点搜索和定位工具',
        author: 'System',
        category: '基础工具'
    },

    features: {
        enabled: true,
        enableHistory: true,
        enableCoordinateSearch: true,
        enableNameSearch: true,
        enableAutoComplete: true
    },

    ui: {
        position: '工具栏-右侧',
        priority: 3,
        icon: 'search.svg',
        tooltip: '地点搜索',
        shortcut: 'S',
        panelWidth: 350,
        panelHeight: 500
    },

    parameters: {
        maxResults: 10,
        searchRadius: 1000,
        historySize: 20,
        debounceDelay: 500,
        minSearchLength: 2
    },

    dependencies: [
        'EventBus',
        'PanelManager'
    ],

    events: {
        emit: [
            'search:activated',
            'search:deactivated', 
            'search:result-found',
            'search:location-selected'
        ],
        listen: [
            'system:ready',
            'toolbar:cleanup'
        ]
    }
};

if (typeof window !== 'undefined') {
    window.SearchConfig = SearchConfig;
}

console.log('🔍 搜索功能模块配置已加载');