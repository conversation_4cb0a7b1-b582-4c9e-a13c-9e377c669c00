# 📏 测量工具模块

## 功能描述
提供距离、面积、角度、高度等多种测量功能，支持实时测量和结果显示。

## 模块信息
- **功能名称**: 测量工具
- **模块类型**: 基础工具
- **优先级**: 高
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)
- ButtonConfig (按钮配置)

## 触发事件
- `measure:activated` - 测量工具激活
- `measure:deactivated` - 测量工具停用
- `measure:result` - 测量结果更新

## 监听事件
- `system:ready` - 系统就绪
- `toolbar:cleanup` - 工具栏清理

## 文件结构
```
测量工具/
├── README.md           ← 本文档
├── config.js          ← 模块配置
├── core/              ← 核心逻辑
│   ├── MeasureHandler.js
│   └── MeasureTool.js
├── ui/                ← 界面逻辑
│   └── MeasureUI.js
├── styles/            ← 样式文件
│   ├── measure.css
│   └── measureTool.css
└── assets/            ← 资源文件
    └── svg/           ← SVG图标
        ├── angle.svg
        ├── area.svg
        ├── distance.svg
        ├── height.svg
        └── measure.svg
```

## 使用方法

### 启用功能
```javascript
// 通过配置启用
FeatureManager.enable('测量工具');

// 或直接调用
const measureUI = MeasureUI.init(viewer, 'toolButtons');
```

### 禁用功能
```javascript
FeatureManager.disable('测量工具');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-左侧",
  "快捷键": "M",
  "默认模式": "距离测量"
}
```

## 开发说明
- 核心逻辑在 `core/` 目录中
- UI 交互在 `ui/` 目录中  
- 所有样式集中在 `styles/` 目录
- 图标资源统一在 `assets/svg/` 目录

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本