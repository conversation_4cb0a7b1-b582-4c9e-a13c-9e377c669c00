# 🔖 书签管理模块

## 功能描述
提供地图视角书签的保存、管理和快速跳转功能。

## 模块信息
- **功能名称**: 书签管理
- **模块类型**: 基础工具
- **优先级**: 中等
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)

## 触发事件
- `bookmark:activated` - 书签工具激活
- `bookmark:deactivated` - 书签工具停用
- `bookmark:saved` - 保存书签
- `bookmark:loaded` - 加载书签

## 文件结构
```
书签管理/
├── README.md           ← 本文档
├── config.js          ← 模块配置
├── core/              ← 核心逻辑
│   ├── BookmarkHandler.js
│   └── BookmarkTool.js
├── ui/                ← 界面逻辑
│   └── BookmarkUI.js
├── styles/            ← 样式文件
│   ├── bookmark.css
│   ├── bookmark-manager.css
│   └── module.css
└── assets/            ← 资源文件
    └── bookmark.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('书签管理');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-右侧",
  "快捷键": "B",
  "最大书签数": 50,
  "自动保存": true
}
```

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本