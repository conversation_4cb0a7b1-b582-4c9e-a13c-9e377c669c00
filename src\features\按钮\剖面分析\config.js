/**
 * 剖面分析配置
 * @module ProfileAnalysisConfig
 */

export const ProfileAnalysisConfig = {
    // 模块基础信息
    id: 'profileAnalysis',
    name: '剖面分析',
    version: '1.0.0',
    
    // UI配置
    ui: {
        // 按钮配置
        button: {
            id: 'toggleProfileAnalysis',
            tooltip: '剖面分析',
            iconPath: 'src/features/按钮/剖面分析/assets/profile-analysis.svg'
        },
        
        // 面板配置
        panel: {
            id: 'profileAnalysisPanel',
            title: '剖面分析',
            width: 320,
            height: 'auto'
        },
        
        // 样式文件
        styles: [
            'src/features/按钮/剖面分析/styles/toolbar-buttons.css'
        ]
    },
    
    // 功能配置
    features: {
        // 分析参数
        analysis: {
            sampleCount: 100,      // 采样点数
            showProfile: true,     // 显示剖面图
            showTerrain: true,     // 显示地形线
            interpolate: true      // 插值处理
        },
        
        // 图表配置
        chart: {
            width: 300,
            height: 200,
            backgroundColor: 'rgba(48, 51, 54, 0.8)',
            gridColor: '#444',
            textColor: '#fff'
        }
    },
    
    // 样式配置
    styles: {
        // 剖面线样式
        line: {
            color: '#00ff00',
            width: 3,
            clampToGround: false
        },
        
        // 采样点样式
        point: {
            color: '#ffff00',
            pixelSize: 6,
            outlineColor: '#000000',
            outlineWidth: 1
        },
        
        // 标签样式
        label: {
            font: '12pt Arial',
            fillColor: '#ffffff',
            outlineColor: '#000000',
            outlineWidth: 1
        }
    }
};