/* 地形挖掘面板样式 */

.terrain-dig-panel {
    position: fixed; /* 保持fixed定位以便使用PanelPositioner */
    /* 移除固定位置 */
    /* left: 20px; */
    /* top: 50px; */
    width: 320px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    color: #333;
    display: none;
    z-index: 1000;
}

.terrain-dig-panel .panel-title {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 6px;
}

.terrain-dig-panel .panel-title::before {
    content: '';
    width: 20px;
    height: 20px;
    background-image: url('../assets/terrain-dig.svg');
    background-size: contain;
    background-repeat: no-repeat;
}

.terrain-dig-panel .input-group {
    margin-bottom: 6px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.terrain-dig-panel .input-group label {
    color: #666;
    font-size: 12px;
    margin-bottom: 2px;
    display: block;
}

.terrain-dig-panel .input-group input[type="range"] {
    width: 80%;
    height: 4px;
    background: #e1e1e1;
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
    margin: 2px 0;
}

.terrain-dig-panel .input-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: #1890ff;
    border-radius: 50%;
    cursor: pointer;
    margin-top: -4px;
}

/* 添加Firefox滑块样式 */
.terrain-dig-panel .input-group input[type="range"]::-moz-range-thumb {
    -moz-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: #1890ff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* 添加Edge/IE滑块样式 */
.terrain-dig-panel .input-group input[type="range"]::-ms-thumb {
    -ms-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: #1890ff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.terrain-dig-panel .texture-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 10px 0;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.terrain-dig-panel .texture-group > label {
    color: #333;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 4px;
}

.terrain-dig-panel .texture-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: white;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #eee;
}

.terrain-dig-panel .texture-item label {
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.terrain-dig-panel .texture-item label::before {
    content: '';
    width: 14px;
    height: 14px;
    background-image: url('/src/images/svg/terrain.svg');
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.6;
}

.terrain-dig-panel .texture-item select {
    width: 100%;
    padding: 8px 10px;
    background: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    color: #333;
    font-size: 12px;
    outline: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.terrain-dig-panel .texture-item select:hover {
    border-color: #1890ff;
    background: white;
}

.terrain-dig-panel .texture-item select:focus {
    border-color: #1890ff;
    background: white;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.terrain-dig-panel .texture-item select option {
    padding: 8px;
    background: white;
}

.terrain-dig-panel .button-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 6px;
}

.terrain-dig-panel .button-group button {
    width: 100%;
    padding: 6px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s ease;
    height: 28px;
    line-height: 1;
    color: white;
}

.terrain-dig-panel .button-group .start-btn {
    background: #1890ff;
}

.terrain-dig-panel .button-group .start-btn:hover {
    background: #40a9ff;
}

.terrain-dig-panel .button-group .clear-btn {
    background: #ff9800;
}

.terrain-dig-panel .button-group .clear-btn:hover {
    background: #ffa726;
}

.terrain-dig-panel .button-group .exit-btn {
    background: #ff4d4f;
}

.terrain-dig-panel .button-group .exit-btn:hover {
    background: #ff7875;
}

/* 绘制状态指示 */
.terrain-dig-panel.drawing {
    border: 2px solid #1890ff;
}

.terrain-dig-panel.drawing .start-btn {
    background: #ff4d4f;
}

.terrain-dig-panel.drawing .start-btn:hover {
    background: #ff7875;
}
