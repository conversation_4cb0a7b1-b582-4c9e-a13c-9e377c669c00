/**
 * 🎮 功能管理器 - 新手友好的功能控制中心
 * 
 * 作用：
 * - 读取配置文件，自动加载功能模块
 * - 提供简单的API来启用/禁用功能
 * - 管理模块之间的依赖关系
 * - 生成功能状态报告
 * 
 * 新手使用指南：
 * 1. 修改 config/features.json 中的 "启用" 属性来控制功能
 * 2. 使用 FeatureManager.enable('功能名') 来启用功能
 * 3. 使用 FeatureManager.disable('功能名') 来禁用功能
 * 4. 使用 FeatureManager.getStatus() 查看所有功能状态
 */

class FeatureManager {
    constructor(viewer) {
        this.viewer = viewer;
        this.config = null;
        this.loadedFeatures = new Map();
        this.featureInstances = new Map();
        this.dependencyGraph = new Map();
        
        // 状态管理
        this.isInitialized = false;
        this.loadErrors = [];
        this.loadSuccess = [];
        
        console.log('🎮 FeatureManager 功能管理器初始化开始...');
    }
    
    /**
     * 初始化功能管理器
     */
    async initialize() {
        try {
            console.log('📋 正在加载功能配置文件...');
            
            // 加载配置文件
            await this.loadConfiguration();
            
            // 分析依赖关系
            this.analyzeDependencies();
            
            // 按优先级自动加载启用的功能
            await this.autoLoadFeatures();
            
            this.isInitialized = true;
            console.log('✅ FeatureManager 初始化完成');
            
            // 生成启动报告
            this.generateStartupReport();
            
            return this.getSystemStatus();
            
        } catch (error) {
            console.error('❌ FeatureManager 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 加载配置文件
     */
    async loadConfiguration() {
        try {
            const response = await fetch('config/features.json');
            if (!response.ok) {
                throw new Error(`配置文件加载失败: ${response.status}`);
            }
            
            this.config = await response.json();
            console.log('📋 配置文件加载成功:', this.config['系统配置']);
            
        } catch (error) {
            console.error('❌ 配置文件加载失败:', error);
            // 使用默认配置
            this.config = this.getDefaultConfig();
            console.warn('⚠️ 使用默认配置');
        }
    }
    
    /**
     * 分析功能依赖关系
     */
    analyzeDependencies() {
        const features = this.config['功能模块'];
        
        Object.keys(features).forEach(featureName => {
            const feature = features[featureName];
            const dependencies = feature['依赖'] || [];
            
            this.dependencyGraph.set(featureName, {
                dependencies: dependencies,
                dependents: []
            });
        });
        
        // 建立反向依赖关系
        this.dependencyGraph.forEach((info, featureName) => {
            info.dependencies.forEach(dep => {
                if (this.dependencyGraph.has(dep)) {
                    this.dependencyGraph.get(dep).dependents.push(featureName);
                }
            });
        });
        
        console.log('🔗 依赖关系分析完成');
    }
    
    /**
     * 自动加载启用的功能
     */
    async autoLoadFeatures() {
        const features = this.config['功能模块'];
        
        // 按优先级排序
        const sortedFeatures = Object.entries(features)
            .filter(([name, config]) => config['启用'] === true)
            .sort(([, a], [, b]) => (a['优先级'] || 999) - (b['优先级'] || 999));
        
        console.log(`🚀 开始加载 ${sortedFeatures.length} 个启用的功能模块...`);
        
        for (const [featureName, featureConfig] of sortedFeatures) {
            try {
                await this.loadFeature(featureName, featureConfig);
                this.loadSuccess.push(featureName);
            } catch (error) {
                console.error(`❌ 功能 ${featureName} 加载失败:`, error);
                this.loadErrors.push({ featureName, error: error.message });
            }
        }
    }
    
    /**
     * 加载单个功能模块
     */
    async loadFeature(featureName, featureConfig) {
        if (this.loadedFeatures.has(featureName)) {
            console.warn(`⚠️ 功能 ${featureName} 已加载`);
            return this.loadedFeatures.get(featureName);
        }
        
        console.log(`🔄 正在加载功能: ${featureName}...`);
        
        // 检查依赖
        const missingDeps = this.checkDependencies(featureName);
        if (missingDeps.length > 0) {
            throw new Error(`缺少依赖: ${missingDeps.join(', ')}`);
        }
        
        try {
            // 根据模块类型加载
            let featureInstance;
            
            if (featureConfig['模块类型'] === 'ES6') {
                // ES6 模块动态导入
                featureInstance = await this.loadES6Module(featureName, featureConfig);
            } else {
                // 传统模块加载
                featureInstance = await this.loadTraditionalModule(featureName, featureConfig);
            }
            
            // 记录加载状态
            this.loadedFeatures.set(featureName, {
                config: featureConfig,
                instance: featureInstance,
                loadTime: Date.now(),
                status: 'loaded'
            });
            
            // 触发加载完成事件
            if (window.EventBus) {
                window.EventBus.emit('feature:loaded', {
                    featureName,
                    config: featureConfig,
                    instance: featureInstance
                });
            }
            
            console.log(`✅ 功能 ${featureName} 加载成功`);
            return featureInstance;
            
        } catch (error) {
            console.error(`❌ 功能 ${featureName} 加载失败:`, error);
            throw error;
        }
    }
    
    /**
     * 加载ES6模块
     */
    async loadES6Module(featureName, featureConfig) {
        const entryFile = featureConfig['入口文件'];
        // 使用绝对路径，从根目录开始
        const modulePath = '/' + entryFile;
        
        try {
            const module = await import(modulePath);
            
            // 查找UI类
            const UIClassName = this.getUIClassName(featureName);
            const UIClass = module[UIClassName];
            
            if (!UIClass) {
                throw new Error(`未找到UI类: ${UIClassName}`);
            }
            
            // 初始化UI
            const instance = UIClass.init(this.viewer, 'toolButtons');
            
            return {
                type: 'ES6',
                module: module,
                instance: instance,
                UIClass: UIClass
            };
            
        } catch (error) {
            console.error(`ES6模块加载失败 ${featureName}:`, error);
            throw error;
        }
    }
    
    /**
     * 加载传统模块
     */
    async loadTraditionalModule(featureName, featureConfig) {
        const entryFile = featureConfig['入口文件'];
        
        // 查找对应的UI类
        const UIClassName = this.getUIClassName(featureName);
        
        if (!window[UIClassName]) {
            throw new Error(`未找到UI类: ${UIClassName}`);
        }
        
        // 初始化UI
        const instance = window[UIClassName].init(this.viewer, 'toolButtons');
        
        return {
            type: 'traditional',
            className: UIClassName,
            instance: instance
        };
    }
    
    /**
     * 获取UI类名
     */
    getUIClassName(featureName) {
        const classNameMap = {
            '测量工具': 'MeasureUI',
            '地形开挖': 'TerrainDigUI',
            '剖面分析': 'ProfileAnalysisUI',
            '搜索功能': 'SearchUI',
            '标记管理': 'AddMarkerUI',
            '书签管理': 'BookmarkUI',
            '漫游飞行': 'RoamFlyUI',
            '场景管理': 'SceneManagerUI',
            '打印功能': 'PrintUI',
            '三维建筑': 'Building3DUI'
        };
        
        return classNameMap[featureName] || featureName + 'UI';
    }
    
    /**
     * 检查依赖关系
     */
    checkDependencies(featureName) {
        const featureConfig = this.config['功能模块'][featureName];
        const dependencies = featureConfig['依赖'] || [];
        const missingDeps = [];
        
        dependencies.forEach(dep => {
            // 检查系统级依赖
            if (dep === 'EventBus' && !window.EventBus) {
                missingDeps.push('EventBus');
            } else if (dep === 'PanelManager' && !window.PanelManager) {
                missingDeps.push('PanelManager');
            } else if (dep === 'WorkerManager' && !window.workerManager) {
                missingDeps.push('WorkerManager');
            } else if (dep === 'ButtonConfig' && !window.ButtonConfig) {
                missingDeps.push('ButtonConfig');
            }
            // 可以添加更多依赖检查
        });
        
        return missingDeps;
    }
    
    /**
     * 启用功能 - 新手友好接口
     */
    async enable(featureName) {
        if (!this.config || !this.config['功能模块'][featureName]) {
            throw new Error(`功能 ${featureName} 不存在`);
        }
        
        console.log(`🔄 启用功能: ${featureName}`);
        
        try {
            // 更新配置
            this.config['功能模块'][featureName]['启用'] = true;
            
            // 如果尚未加载，则加载功能
            if (!this.loadedFeatures.has(featureName)) {
                const featureConfig = this.config['功能模块'][featureName];
                await this.loadFeature(featureName, featureConfig);
            } else {
                // 如果已加载但被禁用，则重新激活
                const featureInfo = this.loadedFeatures.get(featureName);
                featureInfo.status = 'enabled';
            }
            
            // 保存配置更改
            await this.saveConfiguration();
            
            console.log(`✅ 功能 ${featureName} 已启用`);
            
            // 触发事件
            if (window.EventBus) {
                window.EventBus.emit('feature:enabled', { featureName });
            }
            
        } catch (error) {
            console.error(`❌ 启用功能 ${featureName} 失败:`, error);
            throw error;
        }
    }
    
    /**
     * 禁用功能 - 新手友好接口
     */
    async disable(featureName) {
        if (!this.loadedFeatures.has(featureName)) {
            console.warn(`⚠️ 功能 ${featureName} 未加载`);
            return;
        }
        
        console.log(`🔄 禁用功能: ${featureName}`);
        
        try {
            // 更新配置
            this.config['功能模块'][featureName]['启用'] = false;
            
            // 更新状态但不卸载模块（避免重新加载开销）
            const featureInfo = this.loadedFeatures.get(featureName);
            featureInfo.status = 'disabled';
            
            // 可以添加禁用逻辑，如隐藏UI等
            if (featureInfo.instance && featureInfo.instance.disable) {
                featureInfo.instance.disable();
            }
            
            // 保存配置更改
            await this.saveConfiguration();
            
            console.log(`✅ 功能 ${featureName} 已禁用`);
            
            // 触发事件
            if (window.EventBus) {
                window.EventBus.emit('feature:disabled', { featureName });
            }
            
        } catch (error) {
            console.error(`❌ 禁用功能 ${featureName} 失败:`, error);
            throw error;
        }
    }
    
    /**
     * 获取功能状态 - 新手友好接口
     */
    getStatus(featureName = null) {
        if (featureName) {
            // 获取单个功能状态
            if (!this.config || !this.config['功能模块'][featureName]) {
                return null;
            }
            
            const config = this.config['功能模块'][featureName];
            const loadInfo = this.loadedFeatures.get(featureName);
            
            return {
                name: featureName,
                enabled: config['启用'],
                loaded: !!loadInfo,
                status: loadInfo ? loadInfo.status : 'not-loaded',
                description: config['描述'],
                type: config['类型'],
                priority: config['优先级'],
                dependencies: config['依赖'] || []
            };
        } else {
            // 获取所有功能状态
            const allStatus = {};
            
            if (this.config && this.config['功能模块']) {
                Object.keys(this.config['功能模块']).forEach(name => {
                    allStatus[name] = this.getStatus(name);
                });
            }
            
            return allStatus;
        }
    }
    
    /**
     * 生成系统状态报告
     */
    getSystemStatus() {
        const allFeatures = this.config ? Object.keys(this.config['功能模块']) : [];
        const enabledFeatures = allFeatures.filter(name => 
            this.config['功能模块'][name]['启用']
        );
        const loadedFeatures = Array.from(this.loadedFeatures.keys());
        
        return {
            isInitialized: this.isInitialized,
            totalFeatures: allFeatures.length,
            enabledFeatures: enabledFeatures.length,
            loadedFeatures: loadedFeatures.length,
            loadSuccess: this.loadSuccess.length,
            loadErrors: this.loadErrors.length,
            config: this.config,
            features: this.getStatus()
        };
    }
    
    /**
     * 生成启动报告
     */
    generateStartupReport() {
        const status = this.getSystemStatus();
        
        console.log('\n🎉 ===== 功能管理器启动报告 =====');
        console.log(`📊 系统信息: ${this.config['系统配置']['项目名称']} v${this.config['系统配置']['版本']}`);
        console.log(`📦 总功能数: ${status.totalFeatures}`);
        console.log(`✅ 启用功能: ${status.enabledFeatures}`);
        console.log(`🚀 成功加载: ${status.loadSuccess}`);
        console.log(`❌ 加载失败: ${status.loadErrors}`);
        
        if (this.loadErrors.length > 0) {
            console.log('\n❌ 失败详情:');
            this.loadErrors.forEach(error => {
                console.log(`   - ${error.featureName}: ${error.error}`);
            });
        }
        
        if (this.loadSuccess.length > 0) {
            console.log('\n✅ 成功加载的功能:');
            this.loadSuccess.forEach(name => {
                const config = this.config['功能模块'][name];
                console.log(`   - ${name} (${config['类型']})`);
            });
        }
        
        console.log('\n🎮 功能管理器 API:');
        console.log('   featureManager.enable("功能名")   - 启用功能');
        console.log('   featureManager.disable("功能名")  - 禁用功能');
        console.log('   featureManager.getStatus()       - 查看所有状态');
        console.log('   featureManager.generateReport()  - 生成详细报告');
        console.log('==============================\n');
    }
    
    /**
     * 生成详细报告 - 新手友好接口
     */
    generateReport() {
        const status = this.getSystemStatus();
        const report = {
            系统概览: {
                项目名称: this.config['系统配置']['项目名称'],
                版本: this.config['系统配置']['版本'],
                总功能数: status.totalFeatures,
                启用功能数: status.enabledFeatures,
                已加载功能数: status.loadedFeatures
            },
            功能详情: status.features,
            加载成功: this.loadSuccess,
            加载失败: this.loadErrors,
            生成时间: new Date().toLocaleString()
        };
        
        console.log('📊 详细报告:', report);
        return report;
    }
    
    /**
     * 保存配置到本地存储
     */
    async saveConfiguration() {
        try {
            // 保存到localStorage作为备份
            localStorage.setItem('featureManagerConfig', JSON.stringify(this.config));
            console.log('💾 配置已保存到本地存储');
        } catch (error) {
            console.warn('⚠️ 配置保存失败:', error);
        }
    }
    
    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            "系统配置": {
                "项目名称": "Cesium数字地球",
                "版本": "2.0.0",
                "开发者": "Default"
            },
            "功能模块": {},
            "界面配置": {},
            "系统设置": {
                "自动保存": true,
                "错误报告": true
            }
        };
    }
    
    /**
     * 销毁功能管理器
     */
    destroy() {
        // 销毁所有加载的功能
        this.loadedFeatures.forEach((featureInfo, featureName) => {
            if (featureInfo.instance && featureInfo.instance.destroy) {
                try {
                    featureInfo.instance.destroy();
                } catch (error) {
                    console.warn(`销毁功能 ${featureName} 失败:`, error);
                }
            }
        });
        
        // 清理状态
        this.loadedFeatures.clear();
        this.featureInstances.clear();
        this.dependencyGraph.clear();
        
        console.log('🗑️ FeatureManager 已销毁');
    }
}

// 创建全局功能管理器
if (typeof window !== 'undefined') {
    window.FeatureManager = FeatureManager;
}

console.log('🎮 FeatureManager 功能管理器已加载完成');