/**
 * 标记点管理工具
 * 使用纯Cesium API实现地图上标记点的添加、编辑、删除和管理功能
 */
class AddMarkerTool {
    /**
     * 构造函数
     * @param {Object} viewer - Cesium viewer实例
     * @param {Object} options - 配置选项
     */
    constructor(viewer, options = {}) {
        this.viewer = viewer;
        this.options = Object.assign({
            // 默认配置
            markerImageUrl: 'src/features/按钮/标记管理/assets/marker.svg', // 标记图标路径
            editable: true, // 默认可编辑
            useServerStorage: false, // 默认使用本地存储
            storageName: 'cesium_markers' // 本地存储键名
        }, options);
        
        // 初始化状态
        this.isActive = false;
        this._eventHandlers = {};
        this.markers = []; // 存储所有的标记实体
        this.selectedMarker = null; // 当前选中的标记
        this.drawingMode = false; // 是否处于绘制模式
        
        // 初始化事件处理
        this._initEventHandlers();
        
        // 加载已保存的标记点
        this._loadMarkers();
    }
    
    /**
     * 初始化事件处理器
     * @private
     */
    _initEventHandlers() {
        // 创建鼠标事件处理器
        this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas);
        
        // 左键点击事件 - 用于添加标记或选择标记
        this.handler.setInputAction((click) => {
            if (!this.isActive) return;
            
            if (this.drawingMode) {
                // 在绘制模式下，添加新标记
                const cartesian = this._getCartesianFromClick(click.position);
                if (cartesian) {
                    this._addMarker(cartesian);
                    this.drawingMode = false; // 添加完成后退出绘制模式
                }
            } else {
                // 在普通模式下，选择标记
                const pickedObject = this.viewer.scene.pick(click.position);
                if (Cesium.defined(pickedObject) && pickedObject.id && this._isMarkerEntity(pickedObject.id)) {
                    this._selectMarker(pickedObject.id);
                } else {
                    this._deselectMarker();
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        
        // 右键点击事件 - 用于取消绘制
        this.handler.setInputAction(() => {
            if (this.drawingMode) {
                this.drawingMode = false;
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        
        // 鼠标移动事件 - 用于编辑模式
        this.handler.setInputAction((movement) => {
            if (!this.isActive || !this.options.editable || !this.selectedMarker) return;
            
            // 检查是否处于编辑模式且鼠标按下
            if (this.editing) {
                const cartesian = this._getCartesianFromClick(movement.endPosition);
                if (cartesian) {
                    // 更新标记位置
                    this.selectedMarker.position = new Cesium.ConstantPositionProperty(cartesian);
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
    
    /**
     * 从点击位置获取笛卡尔坐标
     * @param {Object} clickPosition - 屏幕点击位置
     * @returns {Cesium.Cartesian3} 笛卡尔坐标
     * @private
     */
    _getCartesianFromClick(clickPosition) {
        // 从屏幕坐标转为笛卡尔坐标
        let cartesian;
        
        // 检查是否有地形
        if (this.viewer.scene.globe.depthTestAgainstTerrain) {
            // 拾取地形或3D模型表面
            cartesian = this.viewer.scene.pickPosition(clickPosition);
        } else {
            // 拾取椭球表面
            const ray = this.viewer.camera.getPickRay(clickPosition);
            cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
        }
        
        // 如果获取失败，尝试使用椭球体拾取
        if (!cartesian) {
            cartesian = this.viewer.scene.camera.pickEllipsoid(
                clickPosition,
                this.viewer.scene.globe.ellipsoid
            );
        }
        
        return cartesian;
    }
    
    /**
     * 检查实体是否是标记点
     * @param {Cesium.Entity} entity - 要检查的实体
     * @returns {Boolean} 是否是标记点
     * @private
     */
    _isMarkerEntity(entity) {
        return entity && entity.properties && entity.properties.isMarker;
    }
    
    /**
     * 添加新标记
     * @param {Cesium.Cartesian3} position - 标记位置
     * @private
     */
    _addMarker(position) {
        const id = new Date().getTime().toString();
        const name = "我的标记";
        
        // 创建标记实体
        const marker = this.viewer.entities.add({
            id: id,
            position: position,
            billboard: {
                image: this.options.markerImageUrl,
                scale: 1.0,
                scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1, 8.0e6, 0.2),
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM
            },
            label: {
                text: name,
                font: 'normal small-caps normal 16px 微软雅黑',
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -36),
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 200000)
            },
            // 添加自定义属性
            properties: {
                isMarker: true,
                markerData: {
                    id: id,
                    name: name,
                    remark: ""
                }
            }
        });
        
        // 保存到标记数组
        this.markers.push(marker);
        
        // 保存到存储
        this._saveMarkers();
        
        // 选中新添加的标记
        this._selectMarker(marker);
        
        // 触发事件
        this._fireEvent('markerAdded', marker);
        this._fireEvent('markersUpdated');
        
        return marker;
    }
    
    /**
     * 选择标记
     * @param {Cesium.Entity} marker - 要选择的标记
     * @private
     */
    _selectMarker(marker) {
        // 先取消之前的选择
        this._deselectMarker();
        
        // 设置当前选中的标记
        this.selectedMarker = marker;
        
        // 显示标记属性编辑框
        if (this.options.editable) {
            // 触发事件以显示编辑UI
            this._fireEvent('markerSelected', marker);
        }
    }
    
    /**
     * 取消选择标记
     * @private
     */
    _deselectMarker() {
        if (this.selectedMarker) {
            // 触发取消选择事件
            this._fireEvent('markerDeselected', this.selectedMarker);
            this.selectedMarker = null;
        }
    }
    
    /**
     * 从本地存储加载标记点
     * @private
     */
    _loadMarkers() {
        if (this.options.useServerStorage) {
            this._loadMarkersFromServer();
        } else {
            this._loadMarkersFromLocalStorage();
        }
    }
    
    /**
     * 从服务器加载标记点
     * @private
     */
    _loadMarkersFromServer() {
        // 根据项目情况实现服务器数据获取
        // 这里提供一个示例实现，实际使用时需要根据项目API调整
        const that = this;
        fetch('api/markers/list')
            .then(response => response.json())
            .then(data => {
                that._importMarkers(data, true);
            })
            .catch(error => {
                console.error('从服务器加载标记失败:', error);
            });
    }
    
    /**
     * 从本地存储加载标记点
     * @private
     */
    _loadMarkersFromLocalStorage() {
        const storageData = localStorage.getItem(this.options.storageName);
        if (storageData) {
            try {
                const markers = JSON.parse(storageData);
                this._importMarkers(markers, true);
            } catch (e) {
                console.error('解析本地存储的标记数据失败:', e);
            }
        }
    }
    
    /**
     * 导入标记点数据
     * @param {Array} data - 标记点数据数组
     * @param {Boolean} clear - 是否清除现有标记
     * @private
     */
    _importMarkers(data, clear = false) {
        if (!data || data.length === 0) return;
        
        if (clear) {
            this.clearMarkers();
        }
        
        const markers = [];
        
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (!item.x || !item.y) continue;
            
            // 创建位置
            const position = Cesium.Cartesian3.fromDegrees(
                item.x, 
                item.y, 
                item.z || 0.0
            );
            
            // 创建标记实体
            const marker = this.viewer.entities.add({
                id: item.id || new Date().getTime().toString(),
                position: position,
                billboard: {
                    image: this.options.markerImageUrl,
                    scale: 1.0,
                    scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1, 8.0e6, 0.2),
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                },
                label: {
                    text: item.name || "我的标记",
                    font: 'normal small-caps normal 16px 微软雅黑',
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    outlineColor: Cesium.Color.BLACK,
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0, -36),
                    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 200000)
                },
                // 添加自定义属性
                properties: {
                    isMarker: true,
                    markerData: {
                        id: item.id || new Date().getTime().toString(),
                        name: item.name || "我的标记",
                        remark: item.remark || ""
                    }
                }
            });
            
            markers.push(marker);
        }
        
        // 添加到标记数组
        this.markers = this.markers.concat(markers);
        
        // 如果有标记，则飞行到标记
        if (markers.length > 0) {
            this.viewer.flyTo(markers, {
                duration: 2.0,
                offset: new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-30), 0)
            });
        }
        
        // 触发事件
        this._fireEvent('markersUpdated');
    }
    
    /**
     * 保存标记到存储
     * @private
     */
    _saveMarkers() {
        if (this.options.useServerStorage) {
            this._saveMarkersToServer();
        } else {
            this._saveMarkersToLocalStorage();
        }
    }
    
    /**
     * 保存标记到服务器
     * @private
     */
    _saveMarkersToServer() {
        // 根据项目情况实现服务器数据保存
        const markersData = this.getMarkersData();
        
        fetch('api/markers/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(markersData)
        })
        .catch(error => {
            console.error('保存标记到服务器失败:', error);
        });
    }
    
    /**
     * 保存标记到本地存储
     * @private
     */
    _saveMarkersToLocalStorage() {
        const markersData = this.getMarkersData();
        localStorage.setItem(this.options.storageName, JSON.stringify(markersData));
    }
    
    /**
     * 获取所有标记的数据
     * @returns {Array} 标记数据数组
     */
    getMarkersData() {
        const data = [];
        
        for (let i = 0; i < this.markers.length; i++) {
            const marker = this.markers[i];
            
            // 忽略已删除的标记
            if (!marker || !marker.position) continue;
            
            // 获取标记属性
            const markerData = marker.properties.markerData.getValue();
            
            // 获取位置
            const position = marker.position.getValue(Cesium.JulianDate.now());
            const cartographic = Cesium.Cartographic.fromCartesian(position);
            const longitude = Cesium.Math.toDegrees(cartographic.longitude);
            const latitude = Cesium.Math.toDegrees(cartographic.latitude);
            const height = cartographic.height;
            
            // 创建数据对象
            const item = {
                id: markerData.id,
                name: markerData.name,
                remark: markerData.remark,
                x: longitude,
                y: latitude,
                z: height
            };
            
            data.push(item);
        }
        
        return data;
    }
    
    /**
     * 触发事件
     * @param {String} eventName - 事件名称
     * @param {Object} eventData - 事件数据
     * @private
     */
    _fireEvent(eventName, eventData) {
        if (this._eventHandlers[eventName]) {
            this._eventHandlers[eventName].forEach(handler => {
                handler(eventData);
            });
        }
    }
    
    // 公共API方法
    
    /**
     * 激活工具
     */
    activate() {
        this.isActive = true;
        this._fireEvent('activated');
    }
    
    /**
     * 停用工具
     */
    deactivate() {
        this.isActive = false;
        this._deselectMarker();
        this.drawingMode = false;
        this._fireEvent('deactivated');
    }
    
    /**
     * 设置是否可编辑
     * @param {Boolean} editable - 是否可编辑
     */
    setEditable(editable) {
        this.options.editable = editable;
    }
    
    /**
     * 停止绘制
     */
    stopDraw() {
        this.drawingMode = false;
    }
    
    /**
     * 开始绘制标记点
     */
    startDraw() {
        this.drawingMode = true;
    }
    
    /**
     * 更新标记信息
     * @param {String} id - 标记ID
     * @param {Object} data - 新的标记数据
     */
    updateMarker(id, data) {
        const marker = this.getMarkerById(id);
        if (!marker) return;
        
        // 更新标记属性
        const markerData = marker.properties.markerData.getValue();
        markerData.name = data.name || markerData.name;
        markerData.remark = data.remark !== undefined ? data.remark : markerData.remark;
        
        // 更新标签文本
        marker.label.text = markerData.name;
        
        // 保存到存储
        this._saveMarkers();
        
        // 触发事件
        this._fireEvent('markerUpdated', marker);
        this._fireEvent('markersUpdated');
    }
    
    /**
     * 删除标记
     * @param {String} id - 标记ID
     */
    deleteMarker(id) {
        const marker = this.getMarkerById(id);
        if (!marker) return;
        
        // 如果当前选中的是要删除的标记，则取消选择
        if (this.selectedMarker && this.selectedMarker.id === id) {
            this._deselectMarker();
        }
        
        // 从Cesium中移除实体
        this.viewer.entities.remove(marker);
        
        // 从标记数组中移除
        this.markers = this.markers.filter(m => m.id !== id);
        
        // 保存到存储
        this._saveMarkers();
        
        // 触发事件
        this._fireEvent('markerDeleted', { id });
        this._fireEvent('markersUpdated');
    }
    
    /**
     * 清除所有标记
     */
    clearMarkers() {
        // 取消当前选择
        this._deselectMarker();
        
        // 移除所有标记实体
        for (let i = 0; i < this.markers.length; i++) {
            this.viewer.entities.remove(this.markers[i]);
        }
        
        // 清空标记数组
        this.markers = [];
        
        // 清除存储
        if (this.options.useServerStorage) {
            // 根据项目情况实现服务器数据清除
            fetch('api/markers/clear', {
                method: 'DELETE'
            }).catch(error => {
                console.error('从服务器清除所有标记失败:', error);
            });
        } else {
            localStorage.removeItem(this.options.storageName);
        }
        
        // 触发事件
        this._fireEvent('markersCleared');
        this._fireEvent('markersUpdated');
    }
    
    /**
     * 定位到指定标记
     * @param {String} id - 标记ID
     */
    centerAt(id) {
        const marker = this.getMarkerById(id);
        if (marker) {
            this.viewer.flyTo(marker, {
                duration: 1.5,
                offset: new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-30), 300)
            });
        }
    }
    
    /**
     * 根据ID获取标记
     * @param {String} id - 标记ID
     * @returns {Cesium.Entity} 标记实体
     */
    getMarkerById(id) {
        return this.markers.find(marker => marker.id === id);
    }
    
    /**
     * 获取所有标记
     * @returns {Array} 标记实体数组
     */
    getMarkers() {
        return this.markers;
    }
    
    /**
     * 获取所有标记数据
     * @returns {Array} 标记数据数组
     */
    getMarkerList() {
        return this.markers.map(marker => {
            return marker.properties.markerData.getValue();
        });
    }
    
    /**
     * 导出标记数据为JSON字符串
     * @returns {String} JSON字符串
     */
    exportToJson() {
        return JSON.stringify(this.getMarkersData());
    }
    
    /**
     * 从JSON数据导入标记
     * @param {String|Array} data - JSON字符串或标记数据数组
     * @param {Boolean} clear - 是否清除现有标记
     */
    importFromJson(data, clear = true) {
        if (!data) return;
        
        let markers;
        if (typeof data === 'string') {
            try {
                markers = JSON.parse(data);
            } catch (e) {
                console.error('解析JSON数据失败:', e);
                return;
            }
        } else if (Array.isArray(data)) {
            markers = data;
        } else {
            console.error('无效的数据格式，需要JSON字符串或数组');
            return;
        }
        
        this._importMarkers(markers, clear);
    }
    
    /**
     * 注册事件监听
     * @param {String} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     */
    on(eventName, handler) {
        if (!this._eventHandlers[eventName]) {
            this._eventHandlers[eventName] = [];
        }
        this._eventHandlers[eventName].push(handler);
    }
    
    /**
     * 移除事件监听
     * @param {String} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     */
    off(eventName, handler) {
        if (this._eventHandlers[eventName]) {
            if (handler) {
                this._eventHandlers[eventName] = this._eventHandlers[eventName].filter(h => h !== handler);
            } else {
                this._eventHandlers[eventName] = [];
            }
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
    module.exports = AddMarkerTool;
} else {
    window.AddMarkerTool = AddMarkerTool;
} 