# 🏢 三维建筑模块

## 功能描述
提供三维建筑物的加载、显示和管理功能，支持多种3D模型格式。

## 模块信息
- **功能名称**: 三维建筑
- **模块类型**: 高级功能
- **优先级**: 中等
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)

## 触发事件
- `building3d:activated` - 3D建筑工具激活
- `building3d:deactivated` - 3D建筑工具停用
- `building3d:model-loaded` - 模型加载完成
- `building3d:model-removed` - 模型移除

## 文件结构
```
三维建筑/
├── README.md           ← 本文档
├── config.js          ← 模块配置
├── core/              ← 核心逻辑
│   └── Building3DTool.js
├── ui/                ← 界面逻辑
│   └── Building3DUI.js
├── styles/            ← 样式文件
│   └── building3d.css
└── assets/            ← 资源文件
    └── building.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('三维建筑');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-左侧",
  "快捷键": "B",
  "支持格式": ["glb", "gltf", "3ds"],
  "默认高度": 50
}
```

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本