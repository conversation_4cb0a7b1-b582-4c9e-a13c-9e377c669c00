# 🖨️ 打印功能模块

## 功能描述
提供地图打印和截图功能，支持多种输出格式和自定义设置。

## 模块信息
- **功能名称**: 打印功能
- **模块类型**: 实用工具
- **优先级**: 低
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)

## 触发事件
- `print:activated` - 打印功能激活
- `print:deactivated` - 打印功能停用
- `print:screenshot-taken` - 截图完成
- `print:exported` - 导出完成

## 文件结构
```
打印功能/
├── README.md           ← 本文档
├── config.js          ← 模块配置
├── core/              ← 核心逻辑
│   └── PrintTool.js
├── ui/                ← 界面逻辑
│   └── PrintUI.js
├── styles/            ← 样式文件
│   └── print.css
└── assets/            ← 资源文件
    └── print.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('打印功能');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-右侧",
  "快捷键": "Ctrl+P",
  "默认格式": "PNG",
  "默认DPI": 300
}
```

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本