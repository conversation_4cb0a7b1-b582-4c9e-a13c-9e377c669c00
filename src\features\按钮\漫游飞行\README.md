# ✈️ 漫游飞行模块

## 功能描述
提供地图漫游飞行功能，支持路径规划和自动飞行浏览。

## 模块信息
- **功能名称**: 漫游飞行
- **模块类型**: 高级功能
- **优先级**: 低
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)

## 触发事件
- `roamfly:activated` - 漫游飞行激活
- `roamfly:deactivated` - 漫游飞行停用
- `roamfly:started` - 开始飞行
- `roamfly:stopped` - 停止飞行

## 文件结构
```
漫游飞行/
├── README.md           ← 本文档
├── config.js          ← 模块配置
├── core/              ← 核心逻辑
│   └── RoamFlyTool.js
├── ui/                ← 界面逻辑
│   ├── RoamFlyUI.js
│   └── templates/
│       └── roamFly.html
├── styles/            ← 样式文件
│   └── roamFly.css
└── assets/            ← 资源文件
    └── roamfly-icon.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('漫游飞行');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-右侧",
  "快捷键": "F",
  "飞行速度": "中等",
  "自动循环": true
}
```

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本