/**
 * 打印功能UI控制器
 */
class PrintUI {
    /**
     * 初始化UI控制器
     * @param {Object} viewer Cesium viewer实例
     * @param {String} container 工具按钮容器ID
     */
    static init(viewer, container) {
        if (!PrintUI.instance) {
            PrintUI.instance = new PrintUI(viewer, container);
        }
        return PrintUI.instance;
    }

    /**
     * 构造函数
     * @param {Object} viewer Cesium viewer实例
     * @param {String} container 工具按钮容器ID
     */
    constructor(viewer, container = 'toolButtons') {
        this.viewer = viewer;
        this.container = container;
        this.tool = null;
        this.panel = null;
        this.isActive = false;
        this.toggleBtnId = 'togglePrint';
        this.panelId = 'printPanel';

        // 不在构造函数中创建UI元素，而是在init方法中创建
        this._addSvgIcon();
    }

    /**
     * 添加SVG图标到文档
     * @private
     */
    _addSvgIcon() {
        // 使用统一的SVG图标管理，不需要单独添加
        // 项目已经在src/images/svg/icons.svg中统一管理SVG图标
    }

    /**
     * 创建工具按钮
     * @private
     */
    _createToolButton() {
        const button = document.createElement('button');
        button.id = PrintConfig.ui.buttonId;
        
        // 使用与其他按钮相同的HTML结构
        button.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24">
                <use xlink:href="#icon-print"></use>
            </svg>
            <span class="tooltip">${PrintConfig.ui.buttonTitle}</span>
        `;
        
        // 将按钮添加到工具容器
        document.getElementById(this.container).appendChild(button);
        button.addEventListener('click', () => this.toggleTool());
    }

    /**
     * 切换工具状态
     */
    toggleTool() {
        if (this.isActive) {
            this.deactivate();
        } else {
            this.activate();
        }
    }

    /**
     * 激活工具
     */
    activate() {
        if (this.isActive) return;

        this.isActive = true;
        this._createPanel();
        this.tool = new PrintTool(this.viewer);
        
        // 更新按钮状态为活动状态
        const button = document.getElementById(PrintConfig.ui.buttonId);
        if (button) {
            button.classList.add('active');
        }
    }

    /**
     * 停用工具
     */
    deactivate() {
        if (!this.isActive) return;

        this.isActive = false;
        
        if (this.tool) {
            this.tool.destroy();
            this.tool = null;
        }

        if (this.panel) {
            // 隐藏面板而不是移除
            this.panel.style.display = 'none';
        }

        // 更新按钮状态为非活动状态
        const button = document.getElementById(PrintConfig.ui.buttonId);
        if (button) {
            button.classList.remove('active');
        }
    }

    /**
     * 创建控制面板
     * @private
     */
    _createPanel() {
        if (this.panel) {
            // 如果面板已存在，直接显示
            if (this.panel.style.display === 'none') {
                this.panel.style.display = 'block';
            }
            return;
        }

        // 创建面板DOM元素
        this.panel = document.createElement('div');
        this.panel.className = 'print-panel';
        this.panel.style.position = 'absolute';
        this.panel.style.right = '80px';
        this.panel.style.top = '80px';
        this.panel.style.zIndex = '1000';
        
        // 创建面板HTML
        this.panel.innerHTML = `
            <div class="print-container">
                <div class="print-header">
                    <h3>打印工具</h3>
                    <button class="close-button">&times;</button>
                </div>
                <div class="print-content">
                    <div class="control-section">
                        <div class="control-item">
                            <label for="printTitle">标题:</label>
                            <input type="text" id="printTitle" class="form-control" placeholder="输入打印标题">
                        </div>
                        <div class="control-item">
                            <label>打印内容:</label>
                            <div>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="includeUI"> 包含UI界面
                                </label>
                            </div>
                        </div>
                        <div class="button-group">
                            <button id="saveImage" class="btn btn-primary">
                                <i class="icon-save"></i> 保存为图片
                            </button>
                            <button id="startPrint" class="btn btn-primary">
                                <i class="icon-print"></i> 打印
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 将面板添加到DOM中
        document.body.appendChild(this.panel);

        // 绑定事件
        this._initPanelEvents();
    }

    /**
     * 初始化面板事件
     * @private
     */
    _initPanelEvents() {
        // 关闭按钮
        const closeButton = this.panel.querySelector('.close-button');
        if (closeButton) {
            closeButton.addEventListener('click', () => this.deactivate());
        }

        // 保存为图片按钮
        const saveImageButton = this.panel.querySelector('#saveImage');
        if (saveImageButton) {
            saveImageButton.addEventListener('click', () => {
                const title = this.panel.querySelector('#printTitle').value;
                const filename = title ? title : PrintConfig.print.defaultFilename;
                
                this.tool.saveImage({ 
                    filename,
                    includeUI: this.panel.querySelector('#includeUI').checked
                });
            });
        }

        // 打印按钮
        const printButton = this.panel.querySelector('#startPrint');
        if (printButton) {
            printButton.addEventListener('click', () => {
                const title = this.panel.querySelector('#printTitle').value;
                
                this.tool.print({
                    title,
                    includeUI: this.panel.querySelector('#includeUI').checked
                });
            });
        }
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <!-- 打印功能面板 -->
        <div id="${this.panelId}" class="print-panel" style="display:none; width: 300px; padding: 15px;">
            <div class="toolbar-panel-title">打印工具</div>
            <div class="print-controls">
                <div class="control-item">
                    <label for="printTitle">标题:</label>
                    <input type="text" id="printTitle" class="form-control" placeholder="输入打印标题">
                </div>
                <div class="control-item">
                    <label>
                        <input type="checkbox" id="includeUI" checked> 包含UI界面
                    </label>
                </div>
                <div class="button-group">
                    <button id="captureScreen" class="btn btn-primary">截图</button>
                    <button id="startPrint" class="btn btn-success">打印</button>
                </div>
            </div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        console.log('打印功能面板已添加到页面');
    }

    /**
     * 初始化打印功能组件及事件绑定
     */
    async init() {
        console.log('正在初始化打印功能UI组件...');
        
        // 创建工具实例
        if (typeof PrintTool !== 'undefined') {
            this.tool = new PrintTool(this.viewer);
        } else {
            console.warn('PrintTool未定义，打印功能可能无法正常工作');
        }
        
        // 绑定事件
        const toggleBtn = document.getElementById(this.toggleBtnId);
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.togglePanel();
            });
        }
        
        // 绑定面板内按钮事件
        this.bindPanelEvents();
        
        console.log('打印功能UI组件初始化完成');
    }

    /**
     * 绑定面板事件
     */
    bindPanelEvents() {
        const captureBtn = document.getElementById('captureScreen');
        if (captureBtn) {
            captureBtn.addEventListener('click', () => {
                if (this.tool) {
                    const title = document.getElementById('printTitle').value || '地图截图';
                    this.tool.captureScreen({ title });
                }
            });
        }

        const printBtn = document.getElementById('startPrint');
        if (printBtn) {
            printBtn.addEventListener('click', () => {
                if (this.tool) {
                    const title = document.getElementById('printTitle').value || '地图打印';
                    const includeUI = document.getElementById('includeUI').checked;
                    this.tool.print({ title, includeUI });
                }
            });
        }
    }

    /**
     * 切换面板显示状态
     */
    togglePanel() {
        const panel = document.getElementById(this.panelId);
        if (!panel) return;
        
        if (panel.style.display === 'none' || !panel.style.display) {
            this.showPanel();
        } else {
            this.hidePanel();
        }
    }

    /**
     * 显示面板
     */
    showPanel() {
        const panel = document.getElementById(this.panelId);
        const toggleBtn = document.getElementById(this.toggleBtnId);
        
        if (panel) {
            // 隐藏其他面板
            const otherPanels = ['searchPanel', 'measureToolContainer', 'terrainDigPanel', 'profileAnalysisPanel', 'bookmarkPanel'];
            otherPanels.forEach(panelId => {
                const otherPanel = document.getElementById(panelId);
                if (otherPanel) {
                    otherPanel.style.display = 'none';
                }
            });
            
            panel.style.display = 'block';
            
            // 设置面板位置
            if (toggleBtn && window.PanelPositioner) {
                try {
                    window.PanelPositioner.setPosition(toggleBtn, panel, {
                        preferredPosition: 'right',
                        gap: 10
                    });
                } catch (e) {
                    panel.style.position = 'fixed';
                    panel.style.top = '100px';
                    panel.style.left = '100px';
                }
            }
        }
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
        const panel = document.getElementById(this.panelId);
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {PrintUI} 打印UI实例
     */
    static init(viewer, toolButtonsId = 'toolButtons') {
        console.log('PrintUI.init 被调用');
        const printUI = new PrintUI(viewer, toolButtonsId);
        
        try {
            // 创建工具按钮
            printUI._createToolButton();
            
            // 检查是否需要添加面板
            if (!document.getElementById(printUI.panelId)) {
                printUI.appendPanelToBody();
            }
            
            // 初始化组件
            printUI.init();
            
            return printUI;
        } catch (error) {
            console.error('PrintUI静态初始化失败:', error);
            return printUI;
        }
    }
}

// 导出到全局作用域
window.PrintUI = PrintUI; 