# 📍 标记管理模块

## 功能描述
提供地图标记点的添加、编辑、删除和管理功能，支持自定义图标和标签。

## 模块信息
- **功能名称**: 标记管理
- **模块类型**: 基础工具
- **优先级**: 中等
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)

## 触发事件
- `marker:activated` - 标记工具激活
- `marker:deactivated` - 标记工具停用
- `marker:added` - 添加标记
- `marker:removed` - 删除标记

## 文件结构
```
标记管理/
├── README.md           ← 本文档
├── config.js          ← 模块配置(待添加)
├── core/              ← 核心逻辑
│   └── AddMarkerTool.js
├── ui/                ← 界面逻辑
│   └── AddMarkerUI.js
├── styles/            ← 样式文件
│   └── addMarker.css
└── assets/            ← 资源文件
    └── marker.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('标记管理');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-左侧",
  "快捷键": "M",
  "默认图标": "marker.svg",
  "允许编辑": true
}
```

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本