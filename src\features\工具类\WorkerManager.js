/**
 * Web Worker管理器 - 统一管理后台计算任务
 * 解决主线程阻塞问题，提升应用响应性
 */
class WorkerManager {
    constructor() {
        this.workers = new Map();
        this.taskQueue = new Map();
        this.requestCounter = 0;
        
        // 初始化各种Worker
        this.initializeWorkers();
        
        console.log('🔧 WorkerManager 初始化完成');
    }
    
    /**
     * 初始化所有Worker
     */
    initializeWorkers() {
        // 地形计算Worker
        this.createWorker('terrain', 'src/workers/terrain-worker.js');
        
        // 几何计算Worker（未来扩展）
        // this.createWorker('geometry', 'src/workers/geometry-worker.js');
        
        // 数据处理Worker（未来扩展）
        // this.createWorker('data', 'src/workers/data-worker.js');
    }
    
    /**
     * 创建Worker实例
     */
    createWorker(name, scriptPath) {
        try {
            const worker = new Worker(scriptPath);
            
            // 设置消息处理器
            worker.onmessage = (e) => {
                this.handleWorkerMessage(name, e);
            };
            
            worker.onerror = (error) => {
                console.error(`Worker ${name} 错误:`, error);
                this.handleWorkerError(name, error);
            };
            
            this.workers.set(name, {
                instance: worker,
                isReady: false,
                activeTasks: new Set(),
                name: name
            });
            
            console.log(`✅ Worker ${name} 创建成功`);
            
            // 标记Worker已准备就绪（移除Turf.js初始化，Worker将自行处理）
            if (name === 'terrain') {
                // Worker会自动标记为就绪状态，无需发送Turf.js对象
                console.log(`📡 ${name} Worker创建完成，等待就绪状态...`);
            }
            
        } catch (error) {
            console.error(`创建Worker ${name} 失败:`, error);
        }
    }
    
    /**
     * 处理Worker消息
     */
    handleWorkerMessage(workerName, event) {
        const { type, data, requestId, error } = event.data;
        
        if (type === 'INIT_COMPLETE') {
            const worker = this.workers.get(workerName);
            if (worker) {
                worker.isReady = true;
                console.log(`🚀 Worker ${workerName} 初始化完成`);
            }
            return;
        }
        
        const task = this.taskQueue.get(requestId);
        if (!task) {
            console.warn(`未找到请求ID: ${requestId}`);
            return;
        }
        
        // 从任务队列中移除
        this.taskQueue.delete(requestId);
        
        // 从Worker活动任务中移除
        const worker = this.workers.get(workerName);
        if (worker) {
            worker.activeTasks.delete(requestId);
        }
        
        if (type === 'ERROR') {
            console.error(`Worker ${workerName} 任务错误:`, error);
            task.reject(new Error(error));
        } else {
            // 添加性能统计
            const endTime = Date.now();
            const executionTime = endTime - task.startTime;
            
            console.log(`⚡ Worker任务完成: ${type}, 耗时: ${executionTime}ms`);
            
            task.resolve({
                type,
                data,
                executionTime,
                workerName
            });
        }
    }
    
    /**
     * 处理Worker错误
     */
    handleWorkerError(workerName, error) {
        const worker = this.workers.get(workerName);
        if (!worker) return;
        
        // 拒绝所有活动任务
        worker.activeTasks.forEach(requestId => {
            const task = this.taskQueue.get(requestId);
            if (task) {
                task.reject(new Error(`Worker ${workerName} 发生错误: ${error.message}`));
                this.taskQueue.delete(requestId);
            }
        });
        
        worker.activeTasks.clear();
        
        // 尝试重新创建Worker
        setTimeout(() => {
            console.log(`🔄 尝试重新创建Worker: ${workerName}`);
            this.recreateWorker(workerName);
        }, 1000);
    }
    
    /**
     * 重新创建Worker
     */
    recreateWorker(workerName) {
        const worker = this.workers.get(workerName);
        if (worker) {
            worker.instance.terminate();
            this.workers.delete(workerName);
        }
        
        // 根据Worker名称重新创建
        switch (workerName) {
            case 'terrain':
                this.createWorker('terrain', 'src/workers/terrain-worker.js');
                break;
            // 可以添加其他Worker类型
        }
    }
    
    /**
     * 向Worker发送任务
     */
    async executeTask(workerName, taskType, data, options = {}) {
        const worker = this.workers.get(workerName);
        
        if (!worker) {
            throw new Error(`Worker ${workerName} 不存在`);
        }
        
        if (!worker.isReady) {
            // 等待Worker就绪
            await this.waitForWorkerReady(workerName, options.timeout || 5000);
        }
        
        const requestId = this.generateRequestId();
        
        return new Promise((resolve, reject) => {
            // 添加到任务队列
            this.taskQueue.set(requestId, {
                resolve,
                reject,
                startTime: Date.now(),
                workerName,
                taskType
            });
            
            // 添加到Worker活动任务
            worker.activeTasks.add(requestId);
            
            // 设置超时（可选）
            if (options.timeout) {
                setTimeout(() => {
                    if (this.taskQueue.has(requestId)) {
                        this.taskQueue.delete(requestId);
                        worker.activeTasks.delete(requestId);
                        reject(new Error(`任务超时: ${taskType}`));
                    }
                }, options.timeout);
            }
            
            // 发送任务到Worker
            this.postMessage(workerName, taskType, data, requestId);
        });
    }
    
    /**
     * 向Worker发送消息
     */
    postMessage(workerName, type, data, requestId = null) {
        const worker = this.workers.get(workerName);
        if (!worker || !worker.instance) {
            console.warn(`Worker ${workerName} 不可用`);
            return;
        }
        
        try {
            worker.instance.postMessage({
                type,
                data,
                requestId: requestId || this.generateRequestId()
            });
        } catch (error) {
            console.error(`向Worker ${workerName} 发送消息失败:`, error);
        }
    }
    
    /**
     * 等待Worker就绪
     */
    waitForWorkerReady(workerName, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const worker = this.workers.get(workerName);
            
            if (!worker) {
                reject(new Error(`Worker ${workerName} 不存在`));
                return;
            }
            
            if (worker.isReady) {
                resolve();
                return;
            }
            
            const checkInterval = setInterval(() => {
                if (worker.isReady) {
                    clearInterval(checkInterval);
                    clearTimeout(timeoutHandle);
                    resolve();
                }
            }, 100);
            
            const timeoutHandle = setTimeout(() => {
                clearInterval(checkInterval);
                reject(new Error(`等待Worker ${workerName} 就绪超时`));
            }, timeout);
        });
    }
    
    /**
     * 生成请求ID
     */
    generateRequestId() {
        return `req_${++this.requestCounter}_${Date.now()}`;
    }
    
    /**
     * 获取Worker状态
     */
    getWorkerStatus() {
        const status = {};
        
        this.workers.forEach((worker, name) => {
            status[name] = {
                isReady: worker.isReady,
                activeTasks: worker.activeTasks.size,
                isAlive: worker.instance !== null
            };
        });
        
        return status;
    }
    
    /**
     * 清理资源
     */
    destroy() {
        // 终止所有Worker
        this.workers.forEach((worker, name) => {
            if (worker.instance) {
                worker.instance.terminate();
                console.log(`🗑️ Worker ${name} 已终止`);
            }
        });
        
        // 清理任务队列
        this.taskQueue.clear();
        this.workers.clear();
        
        console.log('🗑️ WorkerManager 已销毁');
    }
    
    // ================ 便捷方法 ================
    
    /**
     * 地形体积计算
     */
    async calculateVolume(points, depth, heightSamples = null) {
        return this.executeTask('terrain', 'CALCULATE_VOLUME', {
            points,
            depth,
            heightSamples
        });
    }
    
    /**
     * 剖面分析计算
     */
    async calculateProfile(startPoint, endPoint, heightSamples = null, sampleCount = 100) {
        return this.executeTask('terrain', 'CALCULATE_PROFILE', {
            startPoint,
            endPoint,
            heightSamples,
            sampleCount
        });
    }
    
    /**
     * 几何数据处理
     */
    async processGeometry(coordinates, operation, options = {}) {
        return this.executeTask('terrain', 'PROCESS_GEOMETRY', {
            coordinates,
            operation,
            ...options
        });
    }
    
    /**
     * 地形数据分析
     */
    async analyzeTerrainData(elevationData, bounds, analysisType, options = {}) {
        return this.executeTask('terrain', 'ANALYZE_TERRAIN', {
            elevationData,
            bounds,
            analysisType,
            ...options
        });
    }
}

// 创建全局WorkerManager实例
if (typeof window !== 'undefined') {
    window.WorkerManager = WorkerManager;
    window.workerManager = new WorkerManager();
}

console.log('🔧 WorkerManager 已加载完成');