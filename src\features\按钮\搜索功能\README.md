# 🔍 搜索功能模块

## 功能描述
提供地点搜索和定位功能，支持地名搜索、坐标搜索和历史记录。

## 模块信息
- **功能名称**: 搜索功能
- **模块类型**: 基础工具
- **优先级**: 中等
- **状态**: ✅ 已启用

## 依赖关系
- EventBus (事件总线)
- PanelManager (面板管理)

## 触发事件
- `search:activated` - 搜索工具激活
- `search:deactivated` - 搜索工具停用
- `search:result-found` - 搜索到结果
- `search:location-selected` - 选择了位置

## 监听事件
- `system:ready` - 系统就绪
- `toolbar:cleanup` - 工具栏清理

## 文件结构
```
搜索功能/
├── README.md           ← 本文档
├── config.js          ← 模块配置(待添加)
├── core/              ← 核心逻辑
│   └── LocationSearch.js
├── ui/                ← 界面逻辑
│   └── SearchUI.js
├── styles/            ← 样式文件
│   └── search.css
└── assets/            ← 资源文件
    └── search.svg
```

## 使用方法

### 启用功能
```javascript
FeatureManager.enable('搜索功能');
```

### 配置选项
```javascript
{
  "启用": true,
  "位置": "工具栏-右侧",
  "快捷键": "S",
  "搜索API": "默认",
  "保存历史": true
}
```

## 开发说明
- 支持多种搜索API
- 自动保存搜索历史
- 支持坐标和地名搜索

## 更新日志
- v2.0.0: 标准化模块结构
- v1.0.0: 初始版本