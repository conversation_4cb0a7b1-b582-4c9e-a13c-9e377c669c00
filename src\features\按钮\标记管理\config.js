/**
 * 📍 标记管理模块配置文件
 */

const MarkerConfig = {
    module: {
        name: '标记管理',
        id: 'marker',
        version: '2.0.0',
        description: '地图标记点管理工具',
        author: 'System',
        category: '基础工具'
    },

    features: {
        enabled: true,
        allowEdit: true,
        allowDelete: true,
        enableLabels: true,
        enableCustomIcons: true
    },

    ui: {
        position: '工具栏-左侧',
        priority: 4,
        icon: 'marker.svg',
        tooltip: '添加标记点',
        shortcut: 'P',
        panelWidth: 300,
        panelHeight: 400
    },

    parameters: {
        defaultIcon: 'marker.svg',
        iconSize: 32,
        maxMarkers: 100,
        enablePersistence: true,
        labelFontSize: '12px'
    },

    dependencies: [
        'EventBus',
        'PanelManager'
    ],

    events: {
        emit: [
            'marker:activated',
            'marker:deactivated',
            'marker:added',
            'marker:removed',
            'marker:edited'
        ],
        listen: [
            'system:ready',
            'toolbar:cleanup'
        ]
    }
};

if (typeof window !== 'undefined') {
    window.MarkerConfig = MarkerConfig;
}

console.log('📍 标记管理模块配置已加载');