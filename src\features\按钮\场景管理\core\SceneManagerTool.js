/**
 * 场景管理器核心功能类
 * 负责保存/恢复场景状态
 */
import { SceneManagerConfig } from '../config.js';

// 确保Cesium全局对象存在
const Cesium = window.Cesium;
if (!Cesium) {
    console.error('Cesium全局对象未找到，场景管理功能可能无法正常工作');
}

export class SceneManagerTool {
    /**
     * 场景管理器构造函数
     * @param {Object} viewer - Cesium Viewer实例
     * @param {Object} options - 配置选项
     */
    constructor(viewer, options = {}) {
        this.viewer = viewer;
        this.options = { ...SceneManagerConfig, ...options };
        this.scenes = [];
        
        // 加载已保存的场景
        this._loadSavedScenes();
    }
    
    /**
     * 从本地存储加载保存的场景列表
     * @private
     */
    _loadSavedScenes() {
        try {
            const storedScenes = localStorage.getItem(this.options.storageKey);
            if (storedScenes) {
                // 解析存储的场景数据
                let parsedScenes = JSON.parse(storedScenes);
                
                // 确保是数组
                if (!Array.isArray(parsedScenes)) {
                    console.warn('存储的场景数据格式无效，将重置为空数组');
                    parsedScenes = [];
                }
                
                // 过滤掉无效的场景数据
                this.scenes = parsedScenes.filter(scene => scene && scene.id && scene.name);
                
                console.log(`已从本地存储加载${this.scenes.length}个场景`);
            } else {
                console.log('本地存储中没有场景数据');
                this.scenes = [];
            }
        } catch (error) {
            console.error('加载已保存的场景时出错:', error);
            this.scenes = [];
        }
    }
    
    /**
     * 将场景列表保存到本地存储
     * @private
     */
    _saveScenesToStorage() {
        try {
            // 过滤掉无效的场景数据
            const validScenes = this.scenes.filter(scene => scene && scene.id && scene.name);
            
            // 保存到本地存储
            localStorage.setItem(this.options.storageKey, JSON.stringify(validScenes));
            console.log(`已将${validScenes.length}个场景保存到本地存储`);
            return true;
        } catch (error) {
            console.error('保存场景到本地存储时出错:', error);
            return false;
        }
    }
    
    /**
     * 保存当前场景状态
     * @param {string} name - 场景名称
     * @param {string} description - 场景描述(可选)
     * @returns {Object|null} 新保存的场景对象，失败返回null
     */
    saveCurrentScene(name, description = '') {
        try {
            if (!name || typeof name !== 'string') {
                console.error('保存场景失败：场景名称无效');
                return null;
            }
            
            const sceneName = name.trim() || this.options.defaultSceneName;
            
            // 获取当前时间戳作为ID
            const id = Date.now().toString();
            
            // 捕获各种状态数据
            const camera = this._captureCamera();
            const layers = this._captureLayers();
            const markers = this.options.sceneProperties.saveMarkers ? this._captureMarkers() : null;
            const measurements = this.options.sceneProperties.saveMeasurements ? this._captureMeasurements() : null;
            
            // 创建场景数据对象
            const sceneData = {
                id,
                name: sceneName,
                description: description || '',
                timestamp: new Date().toISOString(),
                camera,
                layers,
                markers,
                measurements
            };
            
            // 添加到场景列表
            this.scenes.push(sceneData);
            
            // 如果超出最大数量限制，移除最旧的场景
            if (this.scenes.length > this.options.maxSceneCount) {
                console.log(`场景数量超过限制(${this.options.maxSceneCount})，移除最旧的场景`);
                this.scenes.shift();
            }
            
            // 保存到本地存储
            const saveResult = this._saveScenesToStorage();
            
            if (saveResult) {
                console.log(`已保存场景: ${sceneName}`);
                return sceneData;
            } else {
                console.error('保存场景到本地存储失败');
                return null;
            }
        } catch (error) {
            console.error('保存场景时出错:', error);
            return null;
        }
    }
    
    /**
     * 加载指定的场景
     * @param {string} sceneId - 场景ID
     * @returns {boolean} 是否成功加载
     */
    loadScene(sceneId) {
        if (!sceneId) {
            console.error('加载场景失败：场景ID无效');
            return false;
        }
        
        const scene = this.scenes.find(s => s && s.id === sceneId);
        if (!scene) {
            console.warn(`未找到ID为${sceneId}的场景，可能已被删除`);
            return false;
        }
        
        try {
            // 应用场景设置
            const cameraResult = this._applyCamera(scene.camera);
            const layersResult = this._applyLayers(scene.layers);
            
            // 只有在有标记点数据且功能可用的情况下才应用标记点
            let markersResult = true;
            if (scene.markers && scene.markers.length > 0) {
                markersResult = this._applyMarkers(scene.markers);
            }
            
            // 只有在有测量数据且功能可用的情况下才应用测量结果
            let measurementsResult = true;
            if (scene.measurements && scene.measurements.length > 0 && 
                window.measureTool && typeof window.measureTool.loadMeasurements === 'function') {
                measurementsResult = this._applyMeasurements(scene.measurements);
            }
            
            console.log(`已加载场景: ${scene.name}`, {
                相机: cameraResult ? '成功' : '失败',
                图层: layersResult ? '成功' : '失败',
                标记点: markersResult ? '成功' : '跳过或失败',
                测量结果: measurementsResult ? '成功' : '跳过或失败'
            });
            
            // 只要相机和图层成功加载就认为场景加载成功
            return cameraResult && layersResult;
        } catch (error) {
            console.error(`加载场景时出错: ${error.message}`);
            return false;
        }
    }
    
    /**
     * 删除指定的场景
     * @param {string} sceneId - 场景ID
     * @returns {boolean} 是否成功删除
     */
    deleteScene(sceneId) {
        if (!sceneId) {
            console.error('删除场景失败：场景ID无效');
            return false;
        }
        
        const initialLength = this.scenes.length;
        const sceneToDelete = this.scenes.find(s => s && s.id === sceneId);
        
        // 仅保留ID不匹配的场景
        this.scenes = this.scenes.filter(s => s && s.id !== sceneId);
        
        // 如果长度变化，说明删除成功
        if (this.scenes.length !== initialLength) {
            const saveResult = this._saveScenesToStorage();
            
            if (saveResult) {
                console.log(`已删除ID为${sceneId}的场景`);
                return true;
            } else {
                // 如果保存失败，恢复场景列表
                this._loadSavedScenes();
                console.error('删除场景后保存到本地存储失败');
                return false;
            }
        }
        
        console.warn(`未找到ID为${sceneId}的场景，可能已被删除`);
        return false;
    }
    
    /**
     * 重命名场景
     * @param {string} sceneId - 场景ID
     * @param {string} newName - 新名称
     * @returns {boolean} 是否成功重命名
     */
    renameScene(sceneId, newName) {
        if (!sceneId || !newName || typeof newName !== 'string') {
            console.error('重命名场景失败：参数无效');
            return false;
        }
        
        const scene = this.scenes.find(s => s && s.id === sceneId);
        if (!scene) {
            console.error(`未找到ID为${sceneId}的场景`);
            return false;
        }
        
        const trimmedName = newName.trim();
        if (!trimmedName) {
            console.error('重命名场景失败：新名称不能为空');
            return false;
        }
        
        // 保存旧名称，以备恢复
        const oldName = scene.name;
        
        // 更新名称
        scene.name = trimmedName;
        
        // 保存到本地存储
        const saveResult = this._saveScenesToStorage();
        
        if (saveResult) {
            console.log(`已将场景重命名为: ${trimmedName}`);
            return true;
        } else {
            // 如果保存失败，恢复旧名称
            scene.name = oldName;
            console.error('重命名场景后保存到本地存储失败');
            return false;
        }
    }
    
    /**
     * 获取所有保存的场景
     * @returns {Array} 场景列表
     */
    getScenes() {
        return [...this.scenes];
    }
    
    /**
     * 获取指定ID的场景
     * @param {string} sceneId - 场景ID
     * @returns {Object|null} 场景对象或null
     */
    getScene(sceneId) {
        return this.scenes.find(s => s.id === sceneId) || null;
    }
    
    /**
     * 捕获当前相机状态
     * @private
     * @returns {Object} 相机状态对象
     */
    _captureCamera() {
        if (!this.options.sceneProperties.saveCamera) {
            return null;
        }
        
        const camera = this.viewer.camera;
        const position = camera.position;
        const direction = camera.direction;
        const up = camera.up;
        
        return {
            position: {
                x: position.x,
                y: position.y,
                z: position.z
            },
            direction: {
                x: direction.x,
                y: direction.y,
                z: direction.z
            },
            up: {
                x: up.x,
                y: up.y,
                z: up.z
            },
            heading: camera.heading,
            pitch: camera.pitch,
            roll: camera.roll
        };
    }
    
    /**
     * 应用保存的相机状态
     * @private
     * @param {Object} cameraData - 相机状态数据
     * @returns {boolean} 是否成功应用
     */
    _applyCamera(cameraData) {
        if (!cameraData) {
            console.warn('没有相机数据需要应用');
            return false;
        }
        
        try {
            const camera = this.viewer.camera;
            
            const position = new Cesium.Cartesian3(
                cameraData.position.x,
                cameraData.position.y,
                cameraData.position.z
            );
            
            const direction = new Cesium.Cartesian3(
                cameraData.direction.x,
                cameraData.direction.y,
                cameraData.direction.z
            );
            
            const up = new Cesium.Cartesian3(
                cameraData.up.x,
                cameraData.up.y,
                cameraData.up.z
            );
            
            // 设置相机视角
            camera.setView({
                destination: position,
                orientation: {
                    direction: direction,
                    up: up
                }
            });
            
            console.log('已应用相机设置');
            return true;
        } catch (error) {
            console.error('应用相机设置时出错:', error);
            return false;
        }
    }
    
    /**
     * 捕获当前图层状态
     * @private
     * @returns {Object} 图层状态
     */
    _captureLayers() {
        if (!this.options.sceneProperties.saveLayers) {
            return null;
        }
        
        try {
            const layers = [];
            const imageryLayers = this.viewer.imageryLayers;
            
            // 捕获影像图层
            for (let i = 0; i < imageryLayers.length; i++) {
                const layer = imageryLayers.get(i);
                layers.push({
                    type: 'imagery',
                    index: i,
                    show: layer.show,
                    alpha: layer.alpha
                });
            }
            
            // 捕获实体是否可见
            const entities = this.viewer.entities.values;
            const entityVisibility = {};
            
            for (let i = 0; i < entities.length; i++) {
                const entity = entities[i];
                if (entity.id) {
                    entityVisibility[entity.id] = entity.show;
                }
            }
            
            return {
                imagery: layers,
                entities: entityVisibility
            };
        } catch (error) {
            console.error('捕获图层状态时出错:', error);
            return null;
        }
    }
    
    /**
     * 应用保存的图层状态
     * @private
     * @param {Object} layersData - 图层状态数据
     * @returns {boolean} 是否成功应用
     */
    _applyLayers(layersData) {
        if (!layersData) {
            console.warn('没有图层数据需要应用');
            return false;
        }
        
        try {
            // 应用影像图层设置
            if (layersData.imagery && Array.isArray(layersData.imagery)) {
                const imageryLayers = this.viewer.imageryLayers;
                
                for (let i = 0; i < layersData.imagery.length; i++) {
                    const layerData = layersData.imagery[i];
                    if (layerData && layerData.index < imageryLayers.length) {
                        const layer = imageryLayers.get(layerData.index);
                        if (layer) {
                            layer.show = layerData.show;
                            layer.alpha = layerData.alpha;
                        }
                    }
                }
            }
            
            // 应用实体可见性
            if (layersData.entities && typeof layersData.entities === 'object') {
                const entities = this.viewer.entities.values;
                
                for (let i = 0; i < entities.length; i++) {
                    const entity = entities[i];
                    if (entity && entity.id && layersData.entities[entity.id] !== undefined) {
                        entity.show = layersData.entities[entity.id];
                    }
                }
            }
            
            console.log('已应用图层设置');
            return true;
        } catch (error) {
            console.error('应用图层设置时出错:', error);
            return false;
        }
    }
    
    /**
     * 捕获当前标记点状态
     * @private
     * @returns {Array} 标记点数据
     */
    _captureMarkers() {
        if (!this.options.sceneProperties.saveMarkers || !window.addMarkerTool) {
            return null;
        }
        
        try {
            // 如果添加标记点工具存在，使用其API获取标记
            if (window.addMarkerTool && typeof window.addMarkerTool.getMarkers === 'function') {
                return window.addMarkerTool.getMarkers();
            }
            
            // 备用方法：查找特定类型的实体
            const markers = [];
            const entities = this.viewer.entities.values;
            
            for (let i = 0; i < entities.length; i++) {
                const entity = entities[i];
                // 检查是否是标记点(通常有特定的属性或标签)
                if (entity.billboard && (entity.isMarker || entity.name && entity.name.includes('标记'))) {
                    const position = entity.position.getValue(Cesium.JulianDate.now());
                    const cartographic = Cesium.Cartographic.fromCartesian(position);
                    
                    markers.push({
                        id: entity.id,
                        name: entity.name || '未命名标记',
                        position: {
                            longitude: Cesium.Math.toDegrees(cartographic.longitude),
                            latitude: Cesium.Math.toDegrees(cartographic.latitude),
                            height: cartographic.height
                        },
                        description: entity.description ? entity.description.getValue() : ''
                    });
                }
            }
            
            return markers;
        } catch (error) {
            console.error('捕获标记点时出错:', error);
            return null;
        }
    }
    
    /**
     * 应用保存的标记点
     * @private
     * @param {Array} markersData - 标记点数据
     */
    _applyMarkers(markersData) {
        if (!markersData) return;
        
        try {
            // 如果添加标记点工具存在，使用其API添加标记
            if (window.addMarkerTool && typeof window.addMarkerTool.loadMarkers === 'function') {
                window.addMarkerTool.loadMarkers(markersData);
                console.log(`已应用${markersData.length}个标记点`);
                return;
            }
            
            // 备用方法：手动添加实体
            const existingIds = [];
            // 首先删除当前所有标记点实体
            const entities = this.viewer.entities.values;
            for (let i = entities.length - 1; i >= 0; i--) {
                const entity = entities[i];
                if (entity.billboard && (entity.isMarker || entity.name && entity.name.includes('标记'))) {
                    existingIds.push(entity.id);
                }
            }
            
            // 删除现有标记点
            existingIds.forEach(id => {
                this.viewer.entities.removeById(id);
            });
            
            // 添加保存的标记点
            markersData.forEach(marker => {
                this.viewer.entities.add({
                    id: marker.id,
                    name: marker.name,
                    position: Cesium.Cartesian3.fromDegrees(
                        marker.position.longitude,
                        marker.position.latitude,
                        marker.position.height
                    ),
                    billboard: {
                        image: 'src/features/按钮/标记管理/assets/marker.svg',
                        scale: 0.5,
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                    },
                    description: marker.description,
                    isMarker: true
                });
            });
            
            console.log(`已应用${markersData.length}个标记点`);
        } catch (error) {
            console.error('应用标记点时出错:', error);
        }
    }
    
    /**
     * 捕获当前测量结果
     * @private
     * @returns {Array|null} 测量数据或null
     */
    _captureMeasurements() {
        if (!this.options.sceneProperties.saveMeasurements) {
            return null;
        }
        
        try {
            // 如果测量工具存在并提供了API，使用其API获取测量结果
            if (window.measureTool && typeof window.measureTool.getMeasurements === 'function') {
                const measurements = window.measureTool.getMeasurements();
                console.log(`已捕获${measurements ? measurements.length : 0}个测量结果`);
                return measurements;
            }
            
            // 测量工具API不可用时，尝试备用方法
            console.log('测量工具API不可用，将尝试使用备用方法捕获测量结果');
            
            // 备用方法：获取带有特定属性的实体
            const measurements = [];
            const entities = this.viewer.entities.values;
            
            if (!entities || entities.length === 0) {
                console.log('未找到实体，无法使用备用方法捕获测量结果');
                return null;
            }
            
            let count = 0;
            for (let i = 0; i < entities.length; i++) {
                const entity = entities[i];
                if (entity.isMeasurement || (entity.properties && entity.properties.isMeasurement)) {
                    // 只添加有效的实体
                    if (entity.id) {
                        count++;
                        measurements.push({
                            id: entity.id,
                            type: entity.measurementType || 'unknown'
                        });
                    }
                }
            }
            
            if (count > 0) {
                console.log(`使用备用方法捕获到${count}个测量结果`);
                return measurements;
            } else {
                console.log('未找到测量结果');
                return null;
            }
            
        } catch (error) {
            console.warn('捕获测量结果时出错:', error);
            return null;
        }
    }
    
    /**
     * 应用保存的测量结果
     * @private
     * @param {Array} measurementsData - 测量数据
     * @returns {boolean} 是否成功应用
     */
    _applyMeasurements(measurementsData) {
        if (!measurementsData || measurementsData.length === 0) {
            console.log('没有测量结果需要应用');
            return false;
        }
        
        try {
            // 如果测量工具存在并提供了API，使用其API恢复测量结果
            if (window.measureTool && typeof window.measureTool.loadMeasurements === 'function') {
                window.measureTool.loadMeasurements(measurementsData);
                console.log(`已应用${measurementsData.length}个测量结果`);
                return true;
            }
            
            // 如果API不可用，记录信息但不显示为错误
            console.log('测量工具API不可用，跳过测量结果恢复');
            return false;
            
        } catch (error) {
            console.warn('应用测量结果时出错:', error);
            return false;
        }
    }
    
    /**
     * 导出所有场景数据
     * @returns {string} JSON字符串
     */
    exportScenes() {
        return JSON.stringify(this.scenes);
    }
    
    /**
     * 导入场景数据
     * @param {string} jsonData - JSON字符串
     * @returns {boolean} 是否成功导入
     */
    importScenes(jsonData) {
        try {
            const importedScenes = JSON.parse(jsonData);
            if (Array.isArray(importedScenes)) {
                this.scenes = importedScenes;
                this._saveScenesToStorage();
                console.log(`已导入${importedScenes.length}个场景`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('导入场景数据时出错:', error);
            return false;
        }
    }
    
    /**
     * 清除所有场景
     */
    clearAllScenes() {
        this.scenes = [];
        this._saveScenesToStorage();
        console.log('已清除所有场景');
    }
} 